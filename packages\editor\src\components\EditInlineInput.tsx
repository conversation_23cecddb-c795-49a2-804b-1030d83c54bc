import { Interpolation, Theme } from '@emotion/react';
import React, { useState, useRef, KeyboardEvent, useEffect } from 'react';

type InlineEditProps = {
  text: string;
  onSetText: (text: string) => void;
  placeholder?: string;
  styles?: { placeholderColor: string };
  handleStyle?: (isFocus: boolean) => Interpolation<Theme>;
  inputCss?: Interpolation<Theme>;
  maxLength?: number;
};

const EditInlineInput: React.FC<InlineEditProps> = ({
  text,
  onSetText,
  placeholder = 'Add new text',
  handleStyle,
  styles = { placeholderColor: '#73757b' },
  inputCss = null,
  maxLength = 120
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const [textDraft, setTextDraft] = useState('');
  const handleDoubleClick = (e: { preventDefault: () => void }) => {
    e.preventDefault();
    setTextDraft(text);
    setIsEditing(true);
  };
  const overflowStyle = {
    whiteSpace: 'nowrap',
    textOverflow: 'ellipsis',
    overflow: 'hidden',
    maxWidth: 120,
    display: 'block'
  } as const;
  const handleBlur = () => {
    handleSubmit();
  };
  const handleKeyPress = (
    event: KeyboardEvent<HTMLInputElement>
  ) => {
    setTextDraft(inputRef.current?.value || '');
    if (event.key === 'Enter') {
      handleSubmit();
    } else if (event.key === 'Escape') {
      setIsEditing(false);
      setTextDraft('');
    }
  };

  const handleSubmit = () => {
    if ((inputRef.current?.value.length || 0) > maxLength) {
      console.warn('Maximum is ' + maxLength);
      return;
    }
    setIsFocused(false);
    setIsEditing(false);
    setTextDraft('');
    onSetText(inputRef.current?.value || '');
  };

  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
      inputRef.current.select();
    }
  }, [isEditing]);

  return (
    <div css={handleStyle && handleStyle(isFocused)}>
      <div css={{ minHeight: 18, minWidth: 18 }}>
        {isEditing ? (
          <div
            css={{
              position: 'relative',
              width: '100%',
            }}
          >
            <input
              ref={inputRef}
              defaultValue={text}
              onFocus={() => setIsFocused(true)}
              onBlur={handleBlur}
              onKeyDown={handleKeyPress}
              css={{
                border: 'none',
                outline: 'none',
                backgroundColor: 'transparent',
                width: '100%',
                fontWeight: 'bold',
                color: 'inherit',
                font: 'inherit',
                fontSize: 'inherit',
                lineHeight: 'inherit',
                padding: 0,
                margin: 0,
                whiteSpace: 'nowrap',
                textOverflow: 'ellipsis',
                overflow: 'hidden',
                maxWidth: 450,
                ...(inputCss ? (inputCss as Record<string, any>) : {}),
              }}
            />
          </div>
        ) : (
          <span
            onClick={handleDoubleClick}
            css={{
              color: text ? 'inherit' : styles.placeholderColor,
              fontWeight: 'bold',
              font: 'inherit',
              cursor: 'text',
              userSelect: 'none',
              transition: 'color 0.2s ease',
              '&:hover': {
                opacity: text ? 1 : 0.8,
              },
              ...overflowStyle as Record<string, any>
            }}
          >
            {text || placeholder}
          </span>
        )}
      </div>
    </div>
  );
};

export default EditInlineInput;
