import { css, Global } from '@emotion/react';
import React, { FC, useMemo } from 'react';
import { FontData } from '../../types';
import { handleFontStyle } from 'canva-editor/utils/fontHelper';

export interface FontStyleProps {
    font: FontData;
}

// API configuration - detect environment
const getApiBaseUrl = () => {
  // First check if we have an environment variable
  if (typeof window !== 'undefined' && (window as any).__ENV__?.VITE_API_URL) {
    return (window as any).__ENV__.VITE_API_URL;
  }

  // Then check if we're on localhost (development)
  if (typeof window !== 'undefined' && window.location.hostname === 'localhost') {
    return "http://localhost:4000";
  }

  // Production fallback
  return "http://localhost:4000";
};

const API_BASE_URL = getApiBaseUrl();

const FontStyle: FC<FontStyleProps> = ({ font }) => {
    const fontFaceString = useMemo(() => {
        const fontFaceCss: string[] = [];

        // Convert relative proxy URLs to absolute URLs
        let fontUrl = font.url;
        const isGoogleFont = fontUrl.includes('fonts.gstatic.com') || fontUrl.includes('fonts.googleapis.com');

        if (!isGoogleFont && fontUrl.startsWith('/api/proxy-font/')) {
          fontUrl = `${API_BASE_URL}${fontUrl}`;
        }

        // Determine font format from URL
        const getFormat = (url: string) => {
          if (url.includes('.woff2')) return 'woff2';
          if (url.includes('.woff')) return 'woff';
          if (url.includes('.ttf')) return 'truetype';
          if (url.includes('.otf')) return 'opentype';
          return 'woff2'; // fallback
        };

        fontFaceCss.push(`
            @font-face {
                font-family: '${font.name}';
                ${handleFontStyle(font.style)}
                src: url(${fontUrl}) format('${getFormat(fontUrl)}');
                font-display: block;
            }
        `);
        return fontFaceCss.join('\n');
    }, [font]);

    return (
        <Global
            styles={css`
                ${fontFaceString}
            `}
        />
    );
};

export default React.memo(FontStyle);
