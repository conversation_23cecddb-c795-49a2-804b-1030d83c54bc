/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable no-undef */
/* eslint-disable @typescript-eslint/no-var-requires */
const express = require("express");
const router = express.Router();
const axios = require("axios");
const { Buffer } = require("node:buffer");
const { CLOUD_STORAGE } = require("../config/constants");

// Handle CORS preflight requests
router.options("/proxy-font/*", (req, res) => {
  res.set({
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Origin, Accept',
    'Access-Control-Max-Age': '86400', // Cache preflight for 1 day
  });
  res.status(200).end();
});

// Route to proxy a font by path or complete URL
router.get("/proxy-font/*", async (req, res) => {
  try {
    // Get the full path from the URL
    const fullPath = req.params[0];
    // Decode the URL if it was encoded
    let decodedPath;
    try {
      decodedPath = decodeURIComponent(fullPath);
    } catch (e) {
      console.warn("⚠️ Font Proxy: Could not decode path, using original:", fullPath);
      decodedPath = fullPath;
    }

    // Function to handle the response for font files
    const handleResponse = async (url) => {
      try {


        // For fonts, we need to handle different font types
        const response = await axios.get(url, {
          responseType: "arraybuffer",
          timeout: 15000, // 15 second timeout for fonts
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'font/woff2,font/woff,font/ttf,font/otf,*/*',
            'Accept-Encoding': 'gzip, deflate, br',
            'Referer': 'https://adstudio.foodyqueen.com/'
          }
        });

        // Get content type from response headers or determine from URL
        let contentType = response.headers["content-type"];

        // Always override with proper font MIME type based on file extension
        if (url.includes('.woff2')) {
          contentType = 'font/woff2';
        } else if (url.includes('.woff')) {
          contentType = 'font/woff';
        } else if (url.includes('.ttf')) {
          contentType = 'font/ttf';
        } else if (url.includes('.otf')) {
          contentType = 'font/otf';
        } else {
          // If no extension found, try to use response content-type or fallback
          contentType = contentType || 'font/woff2';
        }

        // Set appropriate headers for font serving
        res.set({
          'Content-Type': contentType,
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Origin, Accept',
          'Access-Control-Expose-Headers': 'Content-Length, Content-Type',
          'Cache-Control': 'public, max-age=86400', // Cache for 1 day
          'Vary': 'Origin',
        });

        // Send the font data directly
        return res.send(Buffer.from(response.data));
      } catch (error) {
        console.error(`❌ Font Proxy: Error fetching font from ${url}:`, error.message);
        if (error.response) {
          console.error(`❌ Font Proxy: Response status: ${error.response.status}`);
          console.error(`❌ Font Proxy: Response headers:`, error.response.headers);
        }
        throw error;
      }
    };


    return await handleResponse(decodedPath);
  } catch (error) {
    console.error("❌ Font Proxy: Error proxying font:", error);

    // Set CORS headers even for error responses
    res.set({
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Origin, Accept',
    });

    res.status(500).json({
      success: false,
      message: "Failed to proxy font",
      error: error.message,
    });
  }
});

module.exports = router;
