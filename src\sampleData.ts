export const data = [
  {
    a: "",
    b: "",
    c: {
      d: {
        e: {
          f: "RootLayer",
        },
        g: {
          h: {
            i: 1640,
            j: 924,
          },
          k: {
            l: 0,
            m: 0,
          },
          n: 0,
          o: "rgb(0, 0, 0)",
          p: null,
          q: null,
        },
        r: false,
        s: [
          "ca_FUsRDxm",
          "ca_gQfMKsq",
          "ca_q7lY9Nd",
          "ca_gyP70R9",
          "ca_WdlRmq7",
          "ca_zhJBSbD",
          "ca_S9XvhU6",
          "ca_c356w5i",
          "ca_WYljDNY",
          "ca_yHg3m0g",
        ],
        t: null,
      },
      ca_FUsRDxm: {
        e: {
          f: "FrameLayer",
        },
        g: {
          k: {
            l: 25.670635809198785,
            m: 356.16647633720015,
          },
          h: {
            i: 433.2307692307692,
            j: 462,
          },
          n: 0,
          ah: 'path("M 34.3461 238.4 C 45.8374 250 60.2653 258.8 76.9915 264.6 C 93.4623 270.2 112.231 273.1 132.788 273.1 C 150.153 273.1 166.496 270.9 181.307 266.7 C 196.501 262.4 209.78 255.7 220.888 246.9 C 231.996 238.1 240.678 227.1 246.935 213.9 C 253.063 201 256.128 185.8 256.128 168.6 L 256.128 160.1 L 178.625 160.1 L 178.625 168.6 C 178.625 177.2 177.476 184.8 175.178 191 C 173.007 196.9 169.943 201.8 166.113 205.5 C 162.41 209.1 158.069 211.7 152.962 213.4 C 147.599 215.2 141.47 216.1 134.703 216.1 C 125.766 216.1 117.977 214.6 111.593 211.8 C 104.954 208.9 99.591 204.7 95.1222 199.1 C 90.398 193.2 86.8229 185.7 84.5247 176.9 C 82.0988 167.7 80.8219 157.1 80.8219 145.5 L 80.8219 127.4 C 80.8219 115.7 81.9711 105.1 84.397 95.9 C 86.6953 87.1 90.1426 79.6 94.7392 73.7 C 99.0803 68.1 104.315 63.9 110.699 61 C 116.956 58.1 124.233 56.7 133.043 56.7 C 139.427 56.7 145.428 57.6 150.536 59.4 C 155.515 61.1 159.856 63.8 163.559 67.4 C 167.39 71.2 170.454 76.1 172.752 82.1 C 175.05 88.4 176.2 96 176.2 104.6 L 176.2 113.1 L 256.255 113.1 L 256.255 104.6 C 256.255 86.9 253.191 71.3 246.935 58.3 C 240.678 45 231.868 33.9 220.76 25.3 C 209.652 16.6 196.245 10.1 181.051 6 C 165.985 2.1 149.77 0 132.66 0 C 112.104 0 93.3347 2.8 76.8638 8.4 C 60.1377 14.2 45.8374 23 34.3461 34.6 C 22.9825 46 14.3002 60.5 8.55461 77.6 C 2.80898 94.3 0 114.1 0 136.6 C 0 159.1 2.80898 178.9 8.55461 195.5 C 14.3002 212.6 22.9825 227 34.3461 238.4 Z")',
          u: 1.6923076923076923,
          p: {
            h: {
              i: 256,
              j: 384,
            },
            k: {
              l: 0,
              m: -55.5,
            },
            n: 0,
            aj: "http://localhost:4000/images/photos/christmas/002.jpg",
            y: "http://localhost:4000/images/photos/christmas/006.jpg",
          },
          ag: 1,
        },
        r: false,
        s: [],
        t: "ROOT",
      },
      ca_gQfMKsq: {
        e: {
          f: "TextLayer",
        },
        g: {
          k: {
            l: 68.12582109382848,
            m: 158.88000467155902,
          },
          h: {
            i: 805.9213867785503,
            j: 237.47011404398754,
            l: 212.81344289254488,
            m: 441.67674400520934,
          },
          u: 3.7693668895871038,
          n: 0,
          v: '<p style="text-align: center;font-family: \'Alumni Sans Pinstripe Regular\';font-size: 45px;color: rgb(255, 255, 255);line-height: 1.4;letter-spacing: normal;"><span style="color: rgb(255, 255, 255);">Canva Editor</span></p>',
          w: [
            {
              x: "Alumni Sans Pinstripe",
              a: "Alumni Sans Pinstripe Regular",
              y: "http://fonts.gstatic.com/s/alumnisanspinstripe/v6/ZgNNjOFFPq_AUJD1umyS30W-Xub8zD1ObhezZ7VIpcDA5w.woff2",
              z: "regular",
              aa: [
                {
                  x: "Alumni Sans Pinstripe",
                  a: "Alumni Sans Pinstripe Italic",
                  y: "http://fonts.gstatic.com/s/alumnisanspinstripe/v6/ZgNDjOFFPq_AUJD1umyS30W-Xub8zD1ObheDYL9Jh8XQ5_cY.woff2",
                  z: "italic",
                },
              ],
            },
          ],
          ab: ["rgb(255, 255, 255)"],
          ac: [45],
          ad: null,
          ag: 1,
        },
        r: false,
        s: [],
        t: "ROOT",
      },
      ca_q7lY9Nd: {
        e: {
          f: "ShapeLayer",
        },
        g: {
          k: {
            l: 1018,
            m: -56.23277270266345,
          },
          h: {
            i: 1102,
            j: 1102,
            l: 589,
            m: 231,
          },
          n: 0,
          ah: "M 64 0 L 256 0 L 192 256 L 0 256 L 64 0 Z",
          u: 4.3046875,
          o: "rgb(230, 0, 18)",
          q: null,
          ag: 1,
          ai: {
            i: 256,
            j: 256,
          },
        },
        r: false,
        s: [],
        t: "ROOT",
      },
      ca_gyP70R9: {
        e: {
          f: "TextLayer",
        },
        g: {
          k: {
            l: 242.28602042458343,
            m: 377.16647633720015,
          },
          h: {
            i: 486.4600528044839,
            j: 82.59182118917317,
            l: 175.73573017317892,
            m: 231.94423856945727,
          },
          u: 1.3109812887170342,
          n: 0,
          v: '<p style="text-align: center;font-family: \'Alfa Slab One Regular\';font-size: 45px;color: rgb(0, 0, 0);line-height: 1.4;letter-spacing: 0.182em;"><strong><span style="color: rgb(0, 0, 0);">WELCOME!</span></strong></p>',
          w: [
            {
              x: "Alfa Slab One",
              a: "Alfa Slab One Regular",
              y: "http://fonts.gstatic.com/s/alfaslabone/v19/6NUQ8FmMKwSEKjnm5-4v-4Jh6dBretWvYmE.woff2",
              z: "regular",
              aa: [],
            },
          ],
          ab: ["rgb(0, 0, 0)"],
          ac: [45],
          ad: {
            a: "outline",
            ae: {
              af: 25,
              o: "rgb(255, 255, 255)",
            },
          },
          ag: 1,
        },
        r: false,
        s: [],
        t: "ROOT",
      },
      ca_WdlRmq7: {
        e: {
          f: "GroupLayer",
        },
        g: {
          k: {
            l: 948.2166993978026,
            m: 215.25829752637338,
          },
          h: {
            i: 489,
            j: 488.99999999999994,
          },
          u: 1,
          n: 0,
          ag: 1,
        },
        r: false,
        s: ["ca_UZkVpUR", "ca_NmkqB5N"],
        t: "ROOT",
      },
      ca_UZkVpUR: {
        e: {
          f: "ShapeLayer",
        },
        g: {
          k: {
            l: 0,
            m: 0,
          },
          h: {
            i: 489,
            j: 489,
            l: 371,
            m: 73.25829752637338,
          },
          n: 0,
          ah: "M 128 0 C 57.3076 0 0 57.3076 0 128 C 0 198.692 57.3076 256 128 256 C 198.692 256 256 198.692 256 128 C 256 57.3076 198.692 0 128 0 Z",
          u: 1.91015625,
          o: "rgb(230, 0, 18)",
          q: null,
          ag: 1,
          ai: {
            i: 256,
            j: 256,
          },
        },
        r: false,
        s: [],
        t: "ca_WdlRmq7",
      },
      ca_NmkqB5N: {
        e: {
          f: "FrameLayer",
        },
        g: {
          k: {
            l: 13.5,
            m: 13.5,
          },
          h: {
            i: 462,
            j: 462,
          },
          n: 0,
          ah: 'path("M 500 250.003 C 500 388.069 388.069 500 250 500 C 111.929 500 0 388.069 0 250.003 C 0 111.93 111.929 0 250 0 C 388.071 0 500 111.93 500 250.003 Z")',
          u: 0.924,
          p: {
            h: {
              i: 500,
              j: 750,
            },
            k: {
              l: 0,
              m: -125,
            },
            n: 0,
            aj: "http://localhost:4000/images/photos/animals/003.jpg",
            y: "http://localhost:4000/images/photos/animals/003.jpg",
          },
          ag: 1,
        },
        r: false,
        s: [],
        t: "ca_WdlRmq7",
      },
      ca_zhJBSbD: {
        e: {
          f: "FrameLayer",
        },
        g: {
          k: {
            l: 242.28602042458337,
            m: 616.1797140049287,
          },
          h: {
            i: 179.11321606542015,
            j: 185.41016506772007,
            l: 267.44073740571537,
            m: 525.1087137227618,
          },
          n: 0,
          ah: 'path("M 84.2927 216.353 L 168.697 216.353 L 185.087 265.1 L 256 265.1 L 164.125 0 L 92.0976 0 L 0 265 L 68.3484 265 L 84.2927 216.353 Z M 126.662 87.765 L 149.408 157.977 L 103.582 157.977 L 126.662 87.765 Z")',
          u: 0.6996610002555474,
          p: {
            h: {
              i: 397.1896955503513,
              j: 265,
            },
            k: {
              l: -70.59484777517565,
              m: 0,
            },
            n: 0,
            aj: "http://localhost:4000/images/photos/christmas/002.jpg",
            y: "http://localhost:4000/images/photos/christmas/009.jpg",
          },
          ag: 1,
        },
        r: false,
        s: [],
        t: "ROOT",
      },
      ca_S9XvhU6: {
        e: {
          f: "FrameLayer",
        },
        g: {
          k: {
            l: 435.50758863447305,
            m: 621.0074157543643,
          },
          h: {
            i: 169.78573193066148,
            j: 175.7547615688488,
            l: 440.9106126133477,
            m: 386.8137962566218,
          },
          n: 0,
          ah: 'path("M 175.868 133.002 L 74.5085 0 L 0 0 L 0 265 L 79.2883 265 L 79.2883 131.998 L 180.789 265 L 256 265 L 256 0 L 175.868 0 Z")',
          u: 0.6632255153541464,
          p: {
            h: {
              i: 256,
              j: 384,
            },
            k: {
              l: 0,
              m: -59.5,
            },
            n: 0,
            aj: "http://localhost:4000/images/photos/christmas/002.jpg",
            y: "http://localhost:4000/images/photos/christmas/002.jpg",
          },
          ag: 1,
        },
        r: false,
        s: [],
        t: "ROOT",
      },
      ca_c356w5i: {
        e: {
          f: "FrameLayer",
        },
        g: {
          k: {
            l: 628.1239746156139,
            m: 621.0074157543643,
          },
          h: {
            i: 175.62073342135525,
            j: 181.79489983069976,
            l: 647.6353507389581,
            m: 529.9364154721974,
          },
          n: 0,
          ah: 'path("M 182.396 0 L 129.735 157.074 L 76.4769 0 L 0 0 L 93.4717 265 L 162.528 265 L 256 0 Z")',
          u: 0.686018489927169,
          p: {
            h: {
              i: 256,
              j: 341.2,
            },
            k: {
              l: 0,
              m: -38.099999999999994,
            },
            n: 0,
            aj: "http://localhost:4000/images/photos/christmas/005.jpg",
            y: "http://localhost:4000/images/photos/christmas/005.jpg",
          },
          ag: 1,
        },
        r: false,
        s: [],
        t: "ROOT",
      },
      ca_WYljDNY: {
        e: {
          f: "FrameLayer",
        },
        g: {
          k: {
            l: 787.0319349205673,
            m: 563.7142794676826,
          },
          h: {
            i: 230.96806507943268,
            j: 239.08803611738148,
            l: 839.5089399037438,
            m: 453.9119638826185,
          },
          n: 0,
          ah: 'path("M 84.2927 216.353 L 168.697 216.353 L 185.087 265.1 L 256 265.1 L 164.125 0 L 92.0976 0 L 0 265 L 68.3484 265 L 84.2927 216.353 Z M 126.662 87.765 L 149.408 157.977 L 103.582 157.977 L 126.662 87.765 Z")',
          u: 0.9022190042165339,
          p: {
            h: {
              i: 256,
              j: 383.6,
            },
            k: {
              l: 0,
              m: -59.30000000000001,
            },
            n: 0,
            aj: "http://localhost:4000/images/photos/birthdays/004.jpg",
            y: "http://localhost:4000/images/photos/birthdays/004.jpg",
          },
          ag: 1,
        },
        r: false,
        s: [],
        t: "ROOT",
      },
      ca_yHg3m0g: {
        e: {
          f: "TextLayer",
        },
        g: {
          k: {
            l: 921.5159674602837,
            m: 762.802315585064,
          },
          h: {
            i: 309.91666666666606,
            j: 143,
          },
          u: 1,
          n: 0,
          v: '<p style="text-align: center;font-family: \'Abel Regular\';font-size: 102px;color: rgb(244, 221, 222);line-height: 1.4;letter-spacing: normal;"><strong><span style="color: rgb(244, 221, 222);">Clone</span></strong></p>',
          w: [
            {
              x: "Abel",
              a: "Abel Regular",
              y: "http://fonts.gstatic.com/s/abel/v18/MwQ5bhbm2POE6V1LPJp6qGI.woff2",
              z: "regular",
              aa: [],
            },
          ],
          ab: ["rgb(244, 221, 222)"],
          ac: [102],
          ad: {
            a: "glitch",
            ae: {
              ak: 14,
              al: 90,
              o: "rgb(176, 57, 62)",
            },
          },
          ag: 1,
        },
        r: false,
        s: [],
        t: "ROOT",
      },
    },
  },
];
export const emptyData = [
  {
    a: "",
    b: "",
    c: {
      d: {
        e: {
          f: "RootLayer",
        },
        g: {
          h: {
            i: 1640,
            j: 924,
          },
          k: {
            l: 0,
            m: 0,
          },
          n: 0,
          o: "rgb(255,255, 255)",
          p: null,
          q: null,
        },
        r: false,
        t: null,
        s: [],
      },
    },
  },
];
