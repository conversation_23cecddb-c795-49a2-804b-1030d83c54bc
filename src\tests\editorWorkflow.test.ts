/**
 * Comprehensive test suite for editor workflow fixes
 * Tests the single-document-per-session design creation and sync system
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { generateObjectId, isValidObjectId, designCreationTracker } from '@/utils/idGenerator';
import { designService } from '@/services/designService';
import { enhancedSyncService } from '@/services/enhancedSyncService';

// Mock axios
vi.mock('axios');

// Mock toast
vi.mock('sonner', () => ({
  toast: {
    loading: vi.fn(),
    success: vi.fn(),
    error: vi.fn(),
    dismiss: vi.fn(),
  },
}));

describe('Editor Workflow - ID Generation', () => {
  it('should generate valid MongoDB ObjectIds', () => {
    const id1 = generateObjectId();
    const id2 = generateObjectId();
    
    expect(id1).toHaveLength(24);
    expect(id2).toHaveLength(24);
    expect(id1).not.toBe(id2);
    expect(isValidObjectId(id1)).toBe(true);
    expect(isValidObjectId(id2)).toBe(true);
  });

  it('should validate ObjectId format correctly', () => {
    expect(isValidObjectId('507f1f77bcf86cd799439011')).toBe(true);
    expect(isValidObjectId('invalid-id')).toBe(false);
    expect(isValidObjectId('507f1f77bcf86cd79943901')).toBe(false); // too short
    expect(isValidObjectId('507f1f77bcf86cd799439011x')).toBe(false); // too long
    expect(isValidObjectId('')).toBe(false);
  });

  it('should generate unique IDs in rapid succession', () => {
    const ids = new Set();
    for (let i = 0; i < 1000; i++) {
      ids.add(generateObjectId());
    }
    expect(ids.size).toBe(1000); // All IDs should be unique
  });
});

describe('Editor Workflow - Design Creation Tracking', () => {
  beforeEach(() => {
    // Clear tracking state
    designCreationTracker.getPendingRequests().forEach(id => {
      designCreationTracker.clearTracking(id);
    });
  });

  it('should prevent duplicate design creation requests', async () => {
    const designId = generateObjectId();
    let callCount = 0;
    
    const mockRequest = () => {
      callCount++;
      return new Promise(resolve => setTimeout(() => resolve({ id: designId }), 100));
    };

    // Start multiple requests with same ID
    const promise1 = designCreationTracker.trackRequest(designId, mockRequest());
    const promise2 = designCreationTracker.trackRequest(designId, mockRequest());
    const promise3 = designCreationTracker.trackRequest(designId, mockRequest());

    // All should resolve to the same promise
    expect(promise1).toBe(promise2);
    expect(promise2).toBe(promise3);

    await Promise.all([promise1, promise2, promise3]);
    
    // Only one actual request should have been made
    expect(callCount).toBe(1);
  });

  it('should track creation progress correctly', () => {
    const designId = generateObjectId();
    const mockRequest = new Promise(resolve => setTimeout(resolve, 100));

    expect(designCreationTracker.isCreationInProgress(designId)).toBe(false);
    expect(designCreationTracker.isDesignCreated(designId)).toBe(false);

    designCreationTracker.trackRequest(designId, mockRequest);

    expect(designCreationTracker.isCreationInProgress(designId)).toBe(true);
    expect(designCreationTracker.isDesignCreated(designId)).toBe(false);
  });
});

describe('Editor Workflow - Design Service', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should create design with pre-generated ID', async () => {
    const mockResponse = {
      data: {
        message: 'Template created successfully',
        template: {
          id: 'test-id-123',
          title: 'Test Design',
        },
      },
    };

    // Mock axios.post
    const axios = await import('axios');
    vi.mocked(axios.default.post).mockResolvedValue(mockResponse);

    const result = await designService.createDesign({
      templateName: 'Test Design',
      userId: 'user-123',
    });

    expect(result.template.id).toBe('test-id-123');
    expect(result.template.title).toBe('Test Design');
  });

  it('should handle design creation conflicts', async () => {
    const conflictError = {
      response: {
        status: 409,
        data: { message: 'Design already exists' },
      },
    };

    const axios = await import('axios');
    vi.mocked(axios.default.post).mockRejectedValue(conflictError);

    await expect(designService.createDesign({
      templateName: 'Test Design',
      userId: 'user-123',
    })).rejects.toThrow('Design with ID');
  });

  it('should retry failed requests', async () => {
    let attemptCount = 0;
    const axios = await import('axios');
    
    vi.mocked(axios.default.post).mockImplementation(() => {
      attemptCount++;
      if (attemptCount < 3) {
        return Promise.reject(new Error('Network error'));
      }
      return Promise.resolve({
        data: {
          message: 'Success',
          template: { id: 'test-id', title: 'Test' },
        },
      });
    });

    const result = await designService.createDesignWithRetry({
      templateName: 'Test Design',
      userId: 'user-123',
    }, {
      retryAttempts: 3,
      showLoadingToast: false,
    });

    expect(attemptCount).toBe(3);
    expect(result.template.id).toBe('test-id');
  });
});

describe('Editor Workflow - Enhanced Sync Service', () => {
  beforeEach(() => {
    enhancedSyncService.destroy();
  });

  afterEach(() => {
    enhancedSyncService.destroy();
  });

  it('should initialize with correct state', () => {
    enhancedSyncService.initialize('user-123', 'design-456', 'Test Design');
    
    const state = enhancedSyncService.getState();
    expect(state.designId).toBe('design-456');
    expect(state.designName).toBe('Test Design');
    expect(state.isDirty).toBe(false);
    expect(state.isSaving).toBe(false);
  });

  it('should detect data changes', () => {
    enhancedSyncService.initialize('user-123');
    
    const initialState = enhancedSyncService.getState();
    expect(initialState.isDirty).toBe(false);

    enhancedSyncService.updateDesignData({ test: 'data' });
    
    const updatedState = enhancedSyncService.getState();
    expect(updatedState.isDirty).toBe(true);
  });

  it('should not trigger save for identical data', () => {
    enhancedSyncService.initialize('user-123');
    
    const testData = { test: 'data' };
    
    enhancedSyncService.updateDesignData(testData);
    const state1 = enhancedSyncService.getState();
    
    enhancedSyncService.updateDesignData(testData);
    const state2 = enhancedSyncService.getState();
    
    // State should not change for identical data
    expect(state1.isDirty).toBe(state2.isDirty);
  });

  it('should handle state subscriptions', () => {
    enhancedSyncService.initialize('user-123');
    
    let receivedState: any = null;
    const unsubscribe = enhancedSyncService.subscribe((state) => {
      receivedState = state;
    });

    enhancedSyncService.updateDesignData({ test: 'data' });
    
    expect(receivedState).not.toBeNull();
    expect(receivedState.isDirty).toBe(true);

    unsubscribe();
  });
});

describe('Editor Workflow - Integration Tests', () => {
  it('should handle rapid design creation clicks', async () => {
    const createDesignRequests = [];
    
    // Simulate rapid clicking
    for (let i = 0; i < 5; i++) {
      createDesignRequests.push(
        designService.createDesign({
          templateName: 'Rapid Test Design',
          userId: 'user-123',
        })
      );
    }

    // Mock successful response
    const axios = await import('axios');
    vi.mocked(axios.default.post).mockResolvedValue({
      data: {
        message: 'Success',
        template: { id: 'test-id', title: 'Test' },
      },
    });

    const results = await Promise.allSettled(createDesignRequests);
    
    // At least one should succeed, others might be deduplicated
    const successful = results.filter(r => r.status === 'fulfilled');
    expect(successful.length).toBeGreaterThan(0);
  });

  it('should handle network failures gracefully', async () => {
    const axios = await import('axios');
    vi.mocked(axios.default.post).mockRejectedValue(new Error('Network error'));

    await expect(designService.createDesign({
      templateName: 'Network Test',
      userId: 'user-123',
    })).rejects.toThrow('Failed to create design');
  });

  it('should validate design ID in URL parameters', () => {
    const validId = generateObjectId();
    const invalidId = 'invalid-id';

    expect(isValidObjectId(validId)).toBe(true);
    expect(isValidObjectId(invalidId)).toBe(false);
  });
});

describe('Editor Workflow - Error Scenarios', () => {
  it('should handle missing user context', async () => {
    await expect(designService.createDesign({
      templateName: 'Test',
      userId: '', // Empty user ID
    })).rejects.toThrow();
  });

  it('should handle malformed API responses', async () => {
    const axios = await import('axios');
    vi.mocked(axios.default.post).mockResolvedValue({
      data: null, // Malformed response
    });

    await expect(designService.createDesign({
      templateName: 'Test',
      userId: 'user-123',
    })).rejects.toThrow();
  });

  it('should handle timeout scenarios', async () => {
    const axios = await import('axios');
    const timeoutError = new Error('Request timed out');
    timeoutError.code = 'ECONNABORTED';
    
    vi.mocked(axios.default.post).mockRejectedValue(timeoutError);

    await expect(designService.createDesign({
      templateName: 'Test',
      userId: 'user-123',
    })).rejects.toThrow('Design creation timed out');
  });
});
