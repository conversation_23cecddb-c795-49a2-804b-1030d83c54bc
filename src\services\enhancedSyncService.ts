/**
 * Enhanced Sync Service for Canva Editor
 * Provides unified synchronization with conflict resolution and proper ID management
 */

import { designService } from './designService';
import { generateObjectId, requestDeduplicator } from '@/utils/idGenerator';
import { toast } from 'sonner';

export interface SyncState {
  designId: string | null;
  designName: string;
  isDirty: boolean;
  isSaving: boolean;
  lastSaved: Date | null;
  lastError: string | null;
  syncStatus: 'idle' | 'saving' | 'error' | 'success';
}

export interface SyncOptions {
  autoSaveInterval?: number;
  debounceDelay?: number;
  maxRetries?: number;
  enableLocalBackup?: boolean;
  showNotifications?: boolean;
}

export interface DesignData {
  [key: string]: any;
}

/**
 * Enhanced Sync Service Class
 */
export class EnhancedSyncService {
  private state: SyncState = {
    designId: null,
    designName: 'Untitled Design',
    isDirty: false,
    isSaving: false,
    lastSaved: null,
    lastError: null,
    syncStatus: 'idle',
  };

  private options: Required<SyncOptions> = {
    autoSaveInterval: 30000, // 30 seconds
    debounceDelay: 3000, // 3 seconds
    maxRetries: 3,
    enableLocalBackup: true,
    showNotifications: true,
  };

  private listeners: Array<(state: SyncState) => void> = [];
  private autoSaveTimer: NodeJS.Timeout | null = null;
  private debounceTimer: NodeJS.Timeout | null = null;
  private lastDataSnapshot: string = '';
  private userId: string | null = null;

  constructor(options: Partial<SyncOptions> = {}) {
    this.options = { ...this.options, ...options };
  }

  /**
   * Initialize the sync service with user context
   */
  initialize(userId: string, designId?: string, designName?: string): void {
    console.log('🔄 Initializing Enhanced Sync Service', { userId, designId, designName });
    
    this.userId = userId;
    this.updateState({
      designId: designId || null,
      designName: designName || 'Untitled Design',
      isDirty: false,
      isSaving: false,
      lastSaved: null,
      lastError: null,
      syncStatus: 'idle',
    });

    this.startAutoSave();
  }

  /**
   * Subscribe to state changes
   */
  subscribe(listener: (state: SyncState) => void): () => void {
    this.listeners.push(listener);
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  /**
   * Get current state
   */
  getState(): SyncState {
    return { ...this.state };
  }

  /**
   * Update design data and trigger sync
   */
  updateDesignData(data: DesignData, designName?: string): void {
    const dataSnapshot = JSON.stringify(data);
    
    // Check if data actually changed
    if (dataSnapshot === this.lastDataSnapshot) {
      return;
    }

    this.lastDataSnapshot = dataSnapshot;
    
    // Update state
    this.updateState({
      isDirty: true,
      designName: designName || this.state.designName,
    });

    // Save to local backup if enabled
    if (this.options.enableLocalBackup) {
      this.saveToLocalBackup(data, this.state.designName);
    }

    // Trigger debounced save
    this.debouncedSave(data);
  }

  /**
   * Force immediate save
   */
  async saveNow(data: DesignData): Promise<boolean> {
    return this.performSave(data, true);
  }

  /**
   * Create a new design with pre-generated ID
   */
  async createNewDesign(
    data: DesignData,
    designName: string,
    options: {
      width?: number;
      height?: number;
      backgroundColor?: string;
      isCoupon?: boolean;
    } = {}
  ): Promise<string | null> {
    if (!this.userId) {
      throw new Error('User ID not set. Call initialize() first.');
    }

    try {
      this.updateState({ isSaving: true, syncStatus: 'saving' });

      const response = await designService.createDesignWithRetry({
        templateName: designName,
        templateDesc: `New design created on ${new Date().toLocaleDateString()}`,
        userId: this.userId,
        packedData: [data], // Wrap in array as expected by API
        previewImage: '', // Will be generated later
        tags: options.isCoupon ? ['coupon'] : [],
        isPublic: false,
        isKiosk: false,
        isCoupon: options.isCoupon || false,
        width: options.width,
        height: options.height,
        backgroundColor: options.backgroundColor,
      });

      const designId = response.template.id;
      
      this.updateState({
        designId,
        designName,
        isDirty: false,
        isSaving: false,
        lastSaved: new Date(),
        lastError: null,
        syncStatus: 'success',
      });

      console.log(`✅ New design created with ID: ${designId}`);
      return designId;

    } catch (error: any) {
      console.error('❌ Failed to create new design:', error);
      
      this.updateState({
        isSaving: false,
        lastError: error.message,
        syncStatus: 'error',
      });

      throw error;
    }
  }

  /**
   * Update design name
   */
  updateDesignName(newName: string): void {
    if (newName !== this.state.designName) {
      this.updateState({
        designName: newName,
        isDirty: true,
      });
    }
  }

  /**
   * Clean up resources
   */
  destroy(): void {
    if (this.autoSaveTimer) {
      clearInterval(this.autoSaveTimer);
    }
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
    }
    this.listeners = [];
  }

  // Private methods

  private updateState(updates: Partial<SyncState>): void {
    this.state = { ...this.state, ...updates };
    this.notifyListeners();
  }

  private notifyListeners(): void {
    this.listeners.forEach(listener => listener(this.state));
  }

  private startAutoSave(): void {
    if (this.autoSaveTimer) {
      clearInterval(this.autoSaveTimer);
    }

    this.autoSaveTimer = setInterval(() => {
      if (this.state.isDirty && !this.state.isSaving) {
        console.log('⏰ Auto-save triggered by timer');
        // We need the current data, but we don't have it here
        // This will be handled by the component using this service
      }
    }, this.options.autoSaveInterval);
  }

  private debouncedSave(data: DesignData): void {
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
    }

    this.debounceTimer = setTimeout(() => {
      if (!this.state.isSaving) {
        this.performSave(data, false);
      }
    }, this.options.debounceDelay);
  }

  private async performSave(data: DesignData, isManual: boolean): Promise<boolean> {
    if (!this.userId) {
      console.error('❌ Cannot save: User ID not set');
      return false;
    }

    if (this.state.isSaving) {
      console.log('⏸️ Save already in progress, skipping');
      return false;
    }

    try {
      this.updateState({ isSaving: true, syncStatus: 'saving' });

      const saveKey = `save-${this.state.designId || 'new'}-${Date.now()}`;
      
      // Use request deduplication for non-manual saves
      const saveOperation = isManual 
        ? this.executeSave(data)
        : requestDeduplicator.deduplicate(saveKey, () => this.executeSave(data));

      await saveOperation;

      this.updateState({
        isDirty: false,
        isSaving: false,
        lastSaved: new Date(),
        lastError: null,
        syncStatus: 'success',
      });

      if (isManual && this.options.showNotifications) {
        toast.success('Design saved successfully!');
      }

      return true;

    } catch (error: any) {
      console.error('❌ Save failed:', error);
      
      this.updateState({
        isSaving: false,
        lastError: error.message,
        syncStatus: 'error',
      });

      if (this.options.showNotifications) {
        toast.error('Failed to save design', {
          description: error.message,
          duration: 5000,
        });
      }

      return false;
    }
  }

  private async executeSave(data: DesignData): Promise<void> {
    // Implementation depends on whether this is a new design or existing one
    if (!this.state.designId) {
      // This is a new design - should have been created via createNewDesign
      throw new Error('Cannot save design without ID. Create design first.');
    }

    // Update existing design
    // This would call the appropriate API endpoint
    console.log(`💾 Saving design ${this.state.designId}...`);
    
    // TODO: Implement actual save logic here
    // For now, simulate save
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  private saveToLocalBackup(data: DesignData, designName: string): void {
    try {
      const backup = {
        designId: this.state.designId,
        designName,
        data,
        timestamp: Date.now(),
      };
      
      localStorage.setItem('design_backup', JSON.stringify(backup));
      console.log('💾 Design backed up to localStorage');
    } catch (error) {
      console.warn('⚠️ Failed to save local backup:', error);
    }
  }
}

// Export singleton instance
export const enhancedSyncService = new EnhancedSyncService();
